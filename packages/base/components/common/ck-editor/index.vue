<script setup>
import {
  AutoImage,
  Autoformat,
  Autosave,
  BlockQuote,
  Bold,
  ClassicEditor,
  Code,
  Emoji,
  Essentials,
  FontBackgroundColor,
  FontColor,
  FontFamily,
  FontSize,
  Fullscreen,
  Heading,
  ImageBlock,
  ImageCaption,
  ImageInline,
  ImageInsert,
  ImageInsertViaUrl,
  ImageResize,
  ImageStyle,
  ImageTextAlternative,
  ImageToolbar,
  ImageUpload,
  Indent,
  IndentBlock,
  Italic,
  Link,
  LinkImage,
  List,
  ListProperties,
  Markdown,
  MediaEmbed,
  Mention,
  Paragraph,
  PasteFromMarkdownExperimental,
  PasteFromOffice,
  SimpleUploadAdapter,
  SpecialCharacters,
  SpecialCharactersArrows,
  SpecialCharactersCurrency,
  SpecialCharactersEssentials,
  SpecialCharactersLatin,
  SpecialCharactersMathematical,
  SpecialCharactersText,
  Strikethrough,
  Subscript,
  Superscript,
  Table,
  TableCaption,
  TableCellProperties,
  TableColumnResize,
  TableLayout,
  TableProperties,
  TableToolbar,
  TextTransformation,
  TodoList,
  Underline,
} from 'ckeditor5'
import { Ckeditor } from '@ckeditor/ckeditor5-vue'
import 'ckeditor5/ckeditor5.css'
// import translations from 'ckeditor5/translations/zh.js'

defineOptions({
  name: 'CKEditor',
})
const data = ref('<p>Hello world!</p>')
const LICENSE_KEY = 'GPL' // or <YOUR_LICENSE_KEY>.
const isLayoutReady = ref(false)
const editor = ClassicEditor
// const config = computed(() => {
//   return {
//     licenseKey: 'GPL', // Or 'GPL'.
//     plugins: [Autoformat, AutoImage, Autosave, BlockQuote, Bold, Code, Emoji, Essentials, FontBackgroundColor, FontColor, FontFamily, FontSize, Fullscreen, Heading, ImageBlock, ImageCaption, ImageInline, ImageInsert, ImageInsertViaUrl, ImageResize, ImageStyle, ImageTextAlternative, ImageToolbar, ImageUpload, Indent, IndentBlock, Italic, Link, LinkImage, List, ListProperties, Markdown, MediaEmbed, Mention, Paragraph, PasteFromMarkdownExperimental, PasteFromOffice, SimpleUploadAdapter, SpecialCharacters, SpecialCharactersArrows, SpecialCharactersCurrency, SpecialCharactersEssentials, SpecialCharactersLatin, SpecialCharactersMathematical, SpecialCharactersText, Strikethrough, Subscript, Superscript, Table, TableCaption, TableCellProperties, TableColumnResize, TableLayout, TableProperties, TableToolbar, TextTransformation, TodoList, Underline],
//     toolbar: {
//       items: [
//         'undo',
//         'redo',
//         '|',
//         'fullscreen',
//         '|',
//         'heading',
//         '|',
//         'fontSize',
//         'fontFamily',
//         'fontColor',
//         'fontBackgroundColor',
//         '|',
//         'bold',
//         'italic',
//         'underline',
//         'strikethrough',
//         'subscript',
//         'superscript',
//         'code',
//         '|',
//         'emoji',
//         'specialCharacters',
//         'link',
//         'insertImage',
//         'mediaEmbed',
//         'insertTable',
//         'insertTableLayout',
//         'blockQuote',
//         '|',
//         'bulletedList',
//         'numberedList',
//         'todoList',
//         'outdent',
//         'indent',
//       ],
//       shouldNotGroupWhenFull: false,
//     },
//     fontFamily: {
//       supportAllValues: true,
//     },
//     fontSize: {
//       options: [10, 12, 14, 'default', 18, 20, 22],
//       supportAllValues: true,
//     },
//     fullscreen: {
//       onEnterCallback: container =>
//         container.classList.add(
//           'editor-container',
//           'editor-container_classic-editor',
//           'editor-container_include-fullscreen',
//           'main-container',
//         ),
//     },
//     heading: {
//       options: [
//         {
//           model: 'paragraph',
//           title: 'Paragraph',
//           class: 'ck-heading_paragraph',
//         },
//         {
//           model: 'heading1',
//           view: 'h1',
//           title: 'Heading 1',
//           class: 'ck-heading_heading1',
//         },
//         {
//           model: 'heading2',
//           view: 'h2',
//           title: 'Heading 2',
//           class: 'ck-heading_heading2',
//         },
//         {
//           model: 'heading3',
//           view: 'h3',
//           title: 'Heading 3',
//           class: 'ck-heading_heading3',
//         },
//         {
//           model: 'heading4',
//           view: 'h4',
//           title: 'Heading 4',
//           class: 'ck-heading_heading4',
//         },
//         {
//           model: 'heading5',
//           view: 'h5',
//           title: 'Heading 5',
//           class: 'ck-heading_heading5',
//         },
//         {
//           model: 'heading6',
//           view: 'h6',
//           title: 'Heading 6',
//           class: 'ck-heading_heading6',
//         },
//       ],
//     },

//   }
// })
const config = computed(() => {
  if (!isLayoutReady.value) {
    return null
  }

  return {
    toolbar: {
      items: [
        'undo',
        'redo',
        '|',
        'fullscreen',
        '|',
        'heading',
        '|',
        'fontSize',
        'fontFamily',
        'fontColor',
        'fontBackgroundColor',
        '|',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        'subscript',
        'superscript',
        'code',
        '|',
        'emoji',
        'specialCharacters',
        'link',
        'insertImage',
        'mediaEmbed',
        'insertTable',
        'insertTableLayout',
        'blockQuote',
        '|',
        'bulletedList',
        'numberedList',
        'todoList',
        'outdent',
        'indent',
      ],
      shouldNotGroupWhenFull: false,
    },
    plugins: [
      Autoformat,
      AutoImage,
      Autosave,
      BlockQuote,
      Bold,
      Code,
      Emoji,
      Essentials,
      FontBackgroundColor,
      FontColor,
      FontFamily,
      FontSize,
      Fullscreen,
      Heading,
      ImageBlock,
      ImageCaption,
      ImageInline,
      ImageInsert,
      ImageInsertViaUrl,
      ImageResize,
      ImageStyle,
      ImageTextAlternative,
      ImageToolbar,
      ImageUpload,
      Indent,
      IndentBlock,
      Italic,
      Link,
      LinkImage,
      List,
      ListProperties,
      Markdown,
      MediaEmbed,
      Mention,
      Paragraph,
      PasteFromMarkdownExperimental,
      PasteFromOffice,
      SimpleUploadAdapter,
      SpecialCharacters,
      SpecialCharactersArrows,
      SpecialCharactersCurrency,
      SpecialCharactersEssentials,
      SpecialCharactersLatin,
      SpecialCharactersMathematical,
      SpecialCharactersText,
      Strikethrough,
      Subscript,
      Superscript,
      Table,
      TableCaption,
      TableCellProperties,
      TableColumnResize,
      TableLayout,
      TableProperties,
      TableToolbar,
      TextTransformation,
      TodoList,
      Underline,
    ],
    fontFamily: {
      supportAllValues: true,
    },
    fontSize: {
      options: [10, 12, 14, 'default', 18, 20, 22],
      supportAllValues: true,
    },
    fullscreen: {
      onEnterCallback: container =>
        container.classList.add(
          'editor-container',
          'editor-container_classic-editor',
          'editor-container_include-fullscreen',
          'main-container',
        ),
    },
    heading: {
      options: [
        {
          model: 'paragraph',
          title: 'Paragraph',
          class: 'ck-heading_paragraph',
        },
        {
          model: 'heading1',
          view: 'h1',
          title: 'Heading 1',
          class: 'ck-heading_heading1',
        },
        {
          model: 'heading2',
          view: 'h2',
          title: 'Heading 2',
          class: 'ck-heading_heading2',
        },
        {
          model: 'heading3',
          view: 'h3',
          title: 'Heading 3',
          class: 'ck-heading_heading3',
        },
        {
          model: 'heading4',
          view: 'h4',
          title: 'Heading 4',
          class: 'ck-heading_heading4',
        },
        {
          model: 'heading5',
          view: 'h5',
          title: 'Heading 5',
          class: 'ck-heading_heading5',
        },
        {
          model: 'heading6',
          view: 'h6',
          title: 'Heading 6',
          class: 'ck-heading_heading6',
        },
      ],
    },
    image: {
      toolbar: [
        'toggleImageCaption',
        'imageTextAlternative',
        '|',
        'imageStyle:inline',
        'imageStyle:wrapText',
        'imageStyle:breakText',
        '|',
        'resizeImage',
      ],
    },
    initialData: '<h2>Congratulations on setting up CKEditor 5! 🎉</h2>\n<p>\n\tYou\'ve successfully created a CKEditor 5 project. This powerful text editor\n\twill enhance your application, enabling rich text editing capabilities that\n\tare customizable and easy to use.\n</p>\n<h3>What\'s next?</h3>\n<ol>\n\t<li>\n\t\t<strong>Integrate into your app</strong>: time to bring the editing into\n\t\tyour application. Take the code you created and add to your application.\n\t</li>\n\t<li>\n\t\t<strong>Explore features:</strong> Experiment with different plugins and\n\t\ttoolbar options to discover what works best for your needs.\n\t</li>\n\t<li>\n\t\t<strong>Customize your editor:</strong> Tailor the editor\'s\n\t\tconfiguration to match your application\'s style and requirements. Or\n\t\teven write your plugin!\n\t</li>\n</ol>\n<p>\n\tKeep experimenting, and don\'t hesitate to push the boundaries of what you\n\tcan achieve with CKEditor 5. Your feedback is invaluable to us as we strive\n\tto improve and evolve. Happy editing!\n</p>\n<h3>Helpful resources</h3>\n<ul>\n\t<li>📝 <a href="https://portal.ckeditor.com/checkout?plan=free">Trial sign up</a>,</li>\n\t<li>📕 <a href="https://ckeditor.com/docs/ckeditor5/latest/installation/index.html">Documentation</a>,</li>\n\t<li>⭐️ <a href="https://github.com/ckeditor/ckeditor5">GitHub</a> (star us if you can!),</li>\n\t<li>🏠 <a href="https://ckeditor.com">CKEditor Homepage</a>,</li>\n\t<li>🧑‍💻 <a href="https://ckeditor.com/ckeditor-5/demo/">CKEditor 5 Demos</a>,</li>\n</ul>\n<h3>Need help?</h3>\n<p>\n\tSee this text, but the editor is not starting up? Check the browser\'s\n\tconsole for clues and guidance. It may be related to an incorrect license\n\tkey if you use premium features or another feature-related requirement. If\n\tyou cannot make it work, file a GitHub issue, and we will help as soon as\n\tpossible!\n</p>\n',
    language: 'zh',
    licenseKey: LICENSE_KEY,
    link: {
      addTargetToExternalLinks: true,
      defaultProtocol: 'https://',
      decorators: {
        toggleDownloadable: {
          mode: 'manual',
          label: 'Downloadable',
          attributes: {
            download: 'file',
          },
        },
      },
    },
    list: {
      properties: {
        styles: true,
        startIndex: true,
        reversed: true,
      },
    },
    mention: {
      feeds: [
        {
          marker: '@',
          feed: [
            /* See: https://ckeditor.com/docs/ckeditor5/latest/features/mentions.html */
          ],
        },
      ],
    },
    placeholder: 'Type or paste your content here!',
    table: {
      contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties'],
    },
    // translations: [translations],
  }
})
onMounted(() => {
  isLayoutReady.value = true
})
</script>

<template>
  <div>
    <Ckeditor
      v-if="editor && config"
      v-model="data"
      :editor="editor"
      :config="config"
    />
  </div>
</template>

<style scoped></style>
